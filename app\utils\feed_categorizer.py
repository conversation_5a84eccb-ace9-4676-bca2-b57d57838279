from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
import json
import asyncio
import copy
import re
from typing import List, Dict, Any, Optional

# --- Constants ---
FALLBACK_RELEVANCE_SCORE_THRESHOLD = 3
MIN_POSTS_PER_CATEGORY = 5
MAX_CONCURRENT_AI_REQUESTS = 5  # Limits concurrent batches to prevent 429 errors
BATCH_SIZE = 5  # Number of posts to process in a single AI prompt

# --- Local Class Definition ---
class FeedPostItem:
    """Local definition of FeedPostItem to avoid circular import."""
    def __init__(self, activity_urn: str, text: str, total_reactions: int = 0,
                 total_comments: int = 0, total_shares: int = 0, author_urn: str = ""):
        self.activity_urn = activity_urn
        self.text = text
        self.total_reactions = total_reactions
        self.total_comments = total_comments
        self.total_shares = total_shares
        self.author_urn = author_urn

# --- Helper Functions ---
def _clean_comment_content(comment: str) -> str:
    """Clean comment content by removing hashtags and normalizing whitespace."""
    if not comment:
        return comment
    comment = re.sub(r'#\w+(?:_\w+)*', '', comment)
    comment = re.sub(r'#', '', comment)
    comment = ' '.join(comment.split())
    return comment.strip()

def _extract_json_from_response(response_text: str) -> str:
    """Extracts a JSON string from a model's text response."""
    if "```json" in response_text:
        start = response_text.find("```json") + 7
        end = response_text.rfind("```")
        if end > start:
            return response_text[start:end].strip()
    elif "```" in response_text:
        start = response_text.find("```") + 3
        end = response_text.rfind("```")
        if end > start:
            return response_text[start:end].strip()
    return response_text

def _post_to_dict(post: Any) -> Dict[str, Any]:
    """Robustly converts a Post object/dict to standardized dict."""
    if isinstance(post, dict):
        return {
            "activity_urn": post.get("activity_urn") or post.get("activityUrn") or post.get("urn") or "",
            "text": post.get("text", ""),
            "total_reactions": post.get("total_reactions", 0),
            "total_comments": post.get("total_comments", 0),
            "total_shares": post.get("total_shares", 0),
            "author_urn": post.get("author_urn", "")
        }

    # Handle Pydantic/Objects
    if hasattr(post, "model_dump"):
        return post.model_dump()
    if hasattr(post, "dict") and callable(post.dict):
        return post.dict()

    urn = getattr(post, "activity_urn", None) or \
          getattr(post, "activityUrn", None) or \
          getattr(post, "urn", None) or \
          ""

    return {
        "activity_urn": urn,
        "text": getattr(post, "text", ""),
        "total_reactions": getattr(post, "total_reactions", 0),
        "total_comments": getattr(post, "total_comments", 0),
        "total_shares": getattr(post, "total_shares", 0),
        "author_urn": getattr(post, "author_urn", "")
    }

# --- Core Categorization Pipeline ---
async def categorize_feed_posts(
    posts: List[FeedPostItem],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """
    Categorizes posts using batched processing for speed and stability.
    """
    print("\n=== FEED CATEGORIZATION START ===")
    print(f"Input posts: {len(posts)}")

    if not posts:
        return []

    # 1. Classify posts by relevance
    classified_posts = await _classify_posts_by_relevance(
        posts, general_persona_keywords, content_persona_keywords, network_persona_keywords
    )

    # 2. Generate hierarchy
    all_posts_flat = classified_posts['highly_relevant'] + classified_posts['moderately_relevant'] + classified_posts['general']
    persona_categories = await _generate_persona_categories_from_posts(
        all_posts_flat, general_persona_keywords, content_persona_keywords, network_persona_keywords
    )
    
    if not persona_categories:
        persona_categories = _create_fallback_categories(general_persona_keywords)

    # 3. Assign posts
    categorized_feed = await _categorize_all_posts_into_persona_categories(
        all_posts_flat, persona_categories, general_persona_keywords, content_persona_keywords, network_persona_keywords
    )

    # 4. Clean & Validate
    categorized_feed = _validate_and_clean_categorized_feed(categorized_feed, posts)

    # 5. Recover Missing Posts (Catch-all)
    categorized_feed = _recover_missing_posts(categorized_feed, posts)

    # 6. Enforce Structure Rules
    categorized_feed = _enforce_minimum_posts_logic(categorized_feed)

    # 7. Sort
    categorized_feed = _sort_categories_by_relevance(categorized_feed, classified_posts)

    # 8. BATCH PROCESSING: Summaries & Recommendations
    # Semaphore limits us to X concurrent BATCHES
    sem = asyncio.Semaphore(MAX_CONCURRENT_AI_REQUESTS)

    processing_tasks = []
    for category in categorized_feed:
        for subcat in category.get("sub_categories", []):
            task = _process_subcategory_batch(
                subcat, 
                category.get("category_name", ""), 
                general_persona_keywords,
                content_persona_keywords, 
                network_persona_keywords,
                sem
            )
            processing_tasks.append(task)
    
    if processing_tasks:
        await asyncio.gather(*processing_tasks)

    # 9. Final Cleanup
    categorized_feed = _remove_empty_categories(categorized_feed)

    total_final_posts = sum(len(subcat.get("posts", [])) for cat in categorized_feed for subcat in cat.get("sub_categories", []))
    print(f"Final output posts: {total_final_posts} out of {len(posts)} input posts")
    print("=== FEED CATEGORIZATION COMPLETE ===")

    return categorized_feed

# --- Logic Functions ---

async def _process_subcategory_batch(
    subcat: Dict[str, Any],
    category_name: str,
    gen_kw: List[str],
    cont_kw: Optional[List[str]],
    net_kw: Optional[List[str]],
    sem: asyncio.Semaphore
):
    """
    Processes a sub-category using batched API calls for speed.
    """
    subcat_posts = subcat.get("posts", [])
    if not subcat_posts:
        return

    # 1. Sort posts by engagement
    subcat_posts.sort(
        key=lambda p: p.get("total_reactions", 0) + p.get("total_comments", 0) + p.get("total_shares", 0),
        reverse=True
    )
    
    subcat["author_member_urns"] = sorted(list({post.get("author_urn") for post in subcat_posts if post.get("author_urn")}))

    # 2. Start Summary Task (Single Task)
    summary_task = _generate_persona_category_summary(
        subcat.get("sub_categories_name", ""), subcat_posts, gen_kw, cont_kw, net_kw, sem
    )

    # 3. Create Recommendation Batches
    rec_tasks = []
    for i in range(0, len(subcat_posts), BATCH_SIZE):
        batch = subcat_posts[i : i + BATCH_SIZE]
        task = _generate_batch_post_recommendations(
            batch, category_name, subcat.get("sub_categories_name", ""), gen_kw, cont_kw, net_kw, sem
        )
        rec_tasks.append(task)

    # 4. Execute all tasks
    results = await asyncio.gather(summary_task, *rec_tasks)

    # 5. Assign Summary
    subcat["summary"] = results[0]
    
    # 6. Map Batch Results back to Posts
    flat_results = []
    for batch_result in results[1:]:
        flat_results.extend(batch_result)
        
    # Create lookup map
    rec_map = {item['activity_urn']: item for item in flat_results if item.get('activity_urn')}

    processed_posts = []
    for post in subcat_posts:
        urn = post.get("activity_urn")
        rec = rec_map.get(urn)
        
        if rec:
            post["recommended_reaction"] = rec.get("reaction", "Like")
            post["suggested_comment"] = _clean_comment_content(rec.get("comment", ""))
        else:
            # Fallback
            post["recommended_reaction"] = "Like"
            post["suggested_comment"] = f"Great insights on {subcat.get('sub_categories_name')}!"
            
        processed_posts.append(post)
        
    subcat["posts"] = processed_posts

async def _generate_batch_post_recommendations(
    posts_batch: List[Dict[str, Any]],
    category_name: str,
    sub_category_name: str,
    gen_kw: List[str],
    cont_kw: Optional[List[str]],
    net_kw: Optional[List[str]],
    sem: asyncio.Semaphore
) -> List[Dict[str, str]]:
    
    # Prepare minimized payload for prompt
    batch_input = []
    for p in posts_batch:
        batch_input.append({
            "activity_urn": p.get("activity_urn"),
            "text": p.get("text", "")[:400] # Truncate to save tokens
        })

    prompt = f"""
    You are a LinkedIn engagement expert. Generate comments for these {len(posts_batch)} posts.
    
    Context:
    - User Persona: {", ".join(gen_kw[:5])}
    - Category: {category_name} > {sub_category_name}
    
    Posts Input:
    {json.dumps(batch_input)}
    
    INSTRUCTIONS:
    1. Return a JSON Object with a "results" array.
    2. The array must have exactly {len(posts_batch)} items.
    3. Each item MUST have: "activity_urn" (matching input), "reaction" (Like/Insightful/etc), and "comment" (100-200 chars).
    4. NO hashtags in comments.
    
    JSON Example:
    {{
      "results": [
        {{ "activity_urn": "urn:...", "reaction": "Like", "comment": "..." }}
      ]
    }}
    """

    async with sem:
        try:
            response = await asyncio.to_thread(model.generate_content, prompt, use_cache=False)
            text = _extract_json_from_response(response.text.strip())
            data = json.loads(text)
            return data.get("results", [])
        except Exception as e:
            print(f"Batch Gen Failed: {e}")
            return [{"activity_urn": p["activity_urn"], "reaction": "Like", "comment": "Interesting perspective!"} for p in posts_batch]

async def _generate_persona_category_summary(sub_name, posts, gen_kw, cont_kw, net_kw, sem):
    sample = [p.get("text", "")[:200] for p in posts[:5]]
    prompt = TEMPLATES["persona_category_summary"].format(
        category_name=sub_name, sample_posts="\n".join(sample),
        general_persona_keywords=", ".join(gen_kw),
        content_persona_keywords=", ".join(cont_kw or []),
        network_persona_keywords=", ".join(net_kw or [])
    )
    async with sem:
        try:
            res = await asyncio.to_thread(model.generate_content, prompt, use_cache=False)
            return res.text.strip()
        except:
            return f"Insights on {sub_name}."

# --- Pipeline Logic Helper Functions ---

def _recover_missing_posts(categorized_feed, original_posts):
    categorized_urns = set()
    categorized_texts = set()
    for cat in categorized_feed:
        for sub in cat.get("sub_categories", []):
            for p in sub.get("posts", []):
                p_dict = _post_to_dict(p)
                if p_dict.get("activity_urn"): categorized_urns.add(p_dict["activity_urn"])
                if p_dict.get("text"): categorized_texts.add(p_dict["text"])

    missing = []
    for p in original_posts:
        pd = _post_to_dict(p)
        if pd.get("activity_urn") and pd["activity_urn"] in categorized_urns: continue
        elif pd.get("text") and pd["text"] in categorized_texts: continue
        missing.append(pd)

    if missing:
        print(f"Recovered {len(missing)} missing posts.")
        target = next((c for c in categorized_feed if "General" in c["category_name"] or "Feed" in c["category_name"]), None)
        if target:
            target["sub_categories"][0]["posts"].extend(missing)
        else:
            categorized_feed.append({"category_name": "General Feed", "sub_categories": [{"sub_categories_name": "Updates", "posts": missing}]})
    return categorized_feed

def _enforce_minimum_posts_logic(feed):
    valid, dissolved = [], []
    for cat in feed:
        cat["sub_categories"] = [s for s in cat.get("sub_categories", []) if s.get("posts")]
        if sum(len(s["posts"]) for s in cat["sub_categories"]) >= MIN_POSTS_PER_CATEGORY:
            valid.append(cat)
        else:
            print(f"Dissolving small category: {cat.get('category_name')}")
            for s in cat["sub_categories"]: dissolved.extend(s["posts"])
            
    if not dissolved: return valid
    
    target = next((c for c in valid if "General" in c["category_name"] or "Network" in c["category_name"]), None)
    if target:
        # Add to first subcat of general
        if target["sub_categories"]:
            target["sub_categories"][0]["posts"].extend(dissolved)
        else:
            target["sub_categories"].append({"sub_categories_name": "General Updates", "posts": dissolved})
    elif len(dissolved) >= MIN_POSTS_PER_CATEGORY:
        valid.append({"category_name": "General Feed", "sub_categories": [{"sub_categories_name": "Updates", "posts": dissolved}]})
    elif valid:
        valid.sort(key=lambda x: sum(len(s["posts"]) for s in x["sub_categories"]), reverse=True)
        valid[0]["sub_categories"].append({"sub_categories_name": "Additional Posts", "posts": dissolved})
    else:
        return [{"category_name": "Feed", "sub_categories": [{"sub_categories_name": "Posts", "posts": dissolved}]}]
    return valid

def _remove_empty_categories(feed):
    non_empty_feed = []
    for category in feed:
        valid_subcats = []
        for subcat in category.get("sub_categories", []):
            # FILTER: Remove ghost posts (no text/ID)
            valid_posts = [p for p in subcat.get("posts", []) if p.get("text") or p.get("activity_urn")]
            if valid_posts:
                subcat["posts"] = valid_posts
                valid_subcats.append(subcat)
        if valid_subcats:
            category["sub_categories"] = valid_subcats
            non_empty_feed.append(category)
    return non_empty_feed

async def _classify_posts_by_relevance(posts, g, c, n):
    data = [{"id": i, "text": _post_to_dict(p).get("text", "")[:300]} for i, p in enumerate(posts)]
    prompt = f"Classify these posts for persona: {', '.join(g)}. Return JSON {{'highly_relevant': [ids], 'moderately_relevant': [ids], 'general': [ids]}}.\n{json.dumps(data)}"
    try:
        res = await asyncio.to_thread(model.generate_content, prompt)
        ids = json.loads(_extract_json_from_response(res.text))
        return {
            'highly_relevant': [posts[i] for i in ids.get('highly_relevant', []) if i < len(posts)],
            'moderately_relevant': [posts[i] for i in ids.get('moderately_relevant', []) if i < len(posts)],
            'general': [posts[i] for i in ids.get('general', []) if i < len(posts)]
        }
    except Exception as e:
        print(f"Classification failed: {e}") 
        return _fallback_post_classification(posts, g, c, n)

async def _generate_persona_categories_from_posts(posts, g, c, n):
    data = [{"id": i, "text": _post_to_dict(p).get("text", "")[:250]} for i, p in enumerate(posts)]
    prompt = TEMPLATES["persona_categories_from_posts"].format(posts_data=json.dumps(data), general_persona_keywords=", ".join(g), content_persona_keywords=", ".join(c or []), network_persona_keywords=", ".join(n or []))
    try:
        res = await asyncio.to_thread(model.generate_content, prompt, use_cache=False)
        return json.loads(_extract_json_from_response(res.text)).get("categories", [])
    except: return _create_fallback_categories(g)

async def _categorize_all_posts_into_persona_categories(posts, cats, g, c, n):
    data = [{"id": i, "text": _post_to_dict(p).get("text", "")[:300]} for i, p in enumerate(posts)]
    prompt = TEMPLATES["persona_post_categorization"].format(posts_data=json.dumps(data), persona_categories=json.dumps(cats), general_persona_keywords=", ".join(g), content_persona_keywords=", ".join(c or []), network_persona_keywords=", ".join(n or []))
    try:
        res = await asyncio.to_thread(model.generate_content, prompt, use_cache=False)
        result = json.loads(_extract_json_from_response(res.text)).get("categorized_posts", [])
        
        # Robust hydration
        for c in result:
            for s in c.get("sub_categories", []):
                hydrated = []
                for p in s["posts"]:
                    pid = p.get("id") if isinstance(p, dict) else p
                    if isinstance(pid, int) and 0 <= pid < len(posts):
                        hydrated.append(_post_to_dict(posts[pid]))
                s["posts"] = hydrated
        return result
    except Exception as e: 
        print(f"Categorization error: {e}")
        return _fallback_persona_post_categorization([_post_to_dict(p) for p in posts], cats)

def _validate_and_clean_categorized_feed(feed, orig):
    seen, clean = set(), []
    for c in feed:
        c_subs = []
        for s in c.get("sub_categories", []):
            u_posts = []
            for p in s["posts"]:
                pd = _post_to_dict(p)
                if pd.get("activity_urn") and pd["activity_urn"] not in seen:
                    seen.add(pd["activity_urn"])
                    u_posts.append(pd)
                elif not pd.get("activity_urn"): u_posts.append(pd)
            if u_posts:
                s["posts"] = u_posts
                c_subs.append(s)
        if c_subs:
            c["sub_categories"] = c_subs
            clean.append(c)
    return clean

def _sort_categories_by_relevance(feed, classif):
    high = {_post_to_dict(p).get("activity_urn") for p in classif['highly_relevant']}
    def sc(c): 
        t = sum(len(s["posts"]) for s in c["sub_categories"])
        p = sum(3 if x.get("activity_urn") in high else 1 for s in c["sub_categories"] for x in s["posts"])
        return p/t if t else 0
    return sorted(feed, key=sc, reverse=True)

def _fallback_post_classification(p, *a): s=len(p)//3; return {'highly_relevant':p[:s],'moderately_relevant':p[s:s*2],'general':p[s*2:]}
def _create_fallback_categories(k): return [{"category_name": x.title(), "sub_categories": [{"sub_categories_name": "General", "posts": []}]} for x in (k[:3] if k else ["Work"])]
def _fallback_persona_post_categorization(p, c):
    if not c: return []
    res = copy.deepcopy(c)
    subs = [(i, j) for i, x in enumerate(res) for j, _ in enumerate(x.get("sub_categories", []))]
    if not subs: return res
    for i, post in enumerate(p):
        idx = subs[i % len(subs)]
        res[idx[0]]["sub_categories"][idx[1]]["posts"].append(post)
    return res