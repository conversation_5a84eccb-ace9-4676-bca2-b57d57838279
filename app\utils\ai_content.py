# ai_content.py
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
import json
import re

def generate_content_ideas(industry, target_audience):
    prompt = TEMPLATES["content_idea"].format(industry=industry, audience=target_audience)
    response = model.generate_content(prompt)
    return response.text

def _category_description(name):
    """Return a meaningful description for a given category name."""
    desc_map = {
        "Leadership": "Insights and strategies on leading teams and organizations.",
        "Innovation": "Latest trends and creative ideas driving change in the industry.",
        "Sustainability": "Practices and news about sustainable business and eco-friendly solutions.",
        "Technology": "Updates and analysis on emerging technologies and digital transformation.",
        "Entrepreneurship": "Advice and stories for starting and growing businesses.",
        "Marketing": "Strategies and tips for effective marketing and brand growth.",
        "Productivity": "Tools and techniques to boost efficiency and performance.",
        "Networking": "Building professional relationships and expanding your network.",
        "Career Development": "Guidance for advancing your career and professional skills.",
        "Diversity": "Discussions on diversity, equity, and inclusion in the workplace.",
        "AI": "Applications and impact of artificial intelligence in various fields.",
        "Finance": "Financial planning, investment, and economic trends.",
        "Health": "Wellness, mental health, and work-life balance tips.",
        "Sales": "Best practices and strategies for sales professionals.",
        "Customer Experience": "Improving customer satisfaction and engagement."
    }
    
    # Check map first (case-insensitive check could be added if needed, but keys are Title Case)
    if name in desc_map:
        return desc_map[name]
    
    try:
        prompt = f"Write a short, professional 1-sentence description for a LinkedIn content category about '{name}'."
        response = model.generate_content(prompt)
        return response.text.strip()
    except Exception:
        return f"Explore the latest trends, insights, and best practices in {name.lower()}."

def generate_content_categories(
    general_persona_keywords=None,
    content_persona_keywords=None,
    network_persona_keywords=None,
    categories=None
):
    """
    Generate professional, native-sounding content categories (1-3 words each).
    
    Features:
    - Categories use standard industry terminology (no "hype" words like 'secrets' or 'crush').
    - Categories are unique.
    - Names are formatted cleanly (Title Case, no hyphens).
    """
    
    # 1. EXPANSION LOGIC: If categories are provided, generate related ones
    if categories and len(categories) > 0:
        result = []
        global_seen_names = set()
        max_attempts = 5  # Prevent infinite loops
        
        for category in categories:
            unique_related = []
            local_seen = set()
            attempts = 0
            while len(unique_related) < 3 and attempts < max_attempts:
                related_categories = _generate_related_categories(
                    category, 
                    general_persona_keywords, 
                    content_persona_keywords, 
                    network_persona_keywords
                )
                for cat in related_categories:
                    # Clean name: remove hyphens, title case
                    clean_name = cat["name"].replace("-", " ").strip().title()
                    cat["name"] = clean_name
                    
                    name_key = clean_name.lower()
                    if name_key not in local_seen and name_key not in global_seen_names:
                        local_seen.add(name_key)
                        global_seen_names.add(name_key)
                        unique_related.append(cat)
                    if len(unique_related) == 3:
                        break
                attempts += 1
            result.append({
                "input": category,
                "related": unique_related
            })
        return result

    # 2. GENERATION LOGIC: Create 10 categories from persona keywords
    
    prompt = f"""
    You are a specialized content strategist for LinkedIn professionals.
    Based on the following persona keywords, create exactly 10 PROFESSIONAL content categories.

    NAMING RULES (CRITICAL):
    1. Use standard Industry Terminology that a native English speaker would use.
    2. Format: Title Case (e.g., "Agile Methods", not "agile-methods").
    3. Length: 1 to 3 words maximum.
    4. NO "Clickbait" or "Hype" words. 
       - BAD: "Defect Crush", "SQL Secrets", "Code Ninja", "Automation Domination", "Testing Hacks".
       - GOOD: "Defect Management", "SQL Optimization", "Clean Code", "Automation Strategy", "Testing Best Practices".
    5. The names must sound like professional tags used in a serious industry publication.

    Persona Context:
    - General Keywords: {', '.join(general_persona_keywords or [])}
    - Content Interests: {', '.join(content_persona_keywords or [])}
    - Network Interests: {', '.join(network_persona_keywords or [])}
    
    Return ONLY a JSON object in this exact format:
    {{
      "categories": [
        {{"name": "Category Name", "description": "Professional description..."}},
        ...
      ]
    }}
    """
    
    try:
        response = model.generate_content(prompt)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
                
        # Parse the JSON response
        result = json.loads(response_text)
        
        if "categories" in result:
            seen_names = set()
            unique_categories = []
            
            for cat in result["categories"]:
                # Explicit cleaning: replace hyphens with spaces, title case
                raw_name = cat["name"]
                clean_name = raw_name.replace("-", " ").replace("_", " ").strip().title()
                cat["name"] = clean_name
                
                name_key = clean_name.lower()
                if name_key not in seen_names:
                    seen_names.add(name_key)
                    unique_categories.append(cat)
            return unique_categories
        else:
            raise ValueError("No 'categories' key found in response")
            
    except Exception as e:
        print(f"Error parsing AI categories: {e}")
        # --- IMPROVED FALLBACK LOGIC ---
        # Prioritize existing keywords directly if they are good, otherwise add safe suffixes.
        categories = []
        seen = set()
        all_keywords = []
        if general_persona_keywords:
            all_keywords.extend(general_persona_keywords)
        if content_persona_keywords:
            all_keywords.extend(content_persona_keywords)
        if network_persona_keywords:
            all_keywords.extend(network_persona_keywords)
            
        # Safe suffixes that always sound professional
        safe_suffixes = ["Trends", "Insights", "Strategy", "Best Practices", "Management", "Development"]
        
        for i, kw in enumerate(all_keywords):
            # Clean special chars
            cleaned = re.sub(r'[^a-zA-Z0-9 ]', '', kw).strip().title()
            
            if cleaned:
                # Logic: If keyword is 2+ words (e.g. "Software Testing"), it's likely a good category name already.
                # If it's 1 word (e.g. "Java"), append a suffix to make it a category (e.g. "Java Insights").
                words = cleaned.split()
                
                if len(words) >= 2:
                    name = cleaned
                else:
                    suffix = safe_suffixes[i % len(safe_suffixes)]
                    name = f"{cleaned} {suffix}"
                
                if name not in seen:
                    seen.add(name)
                    description = _category_description(name)
                    categories.append({
                        "name": name,
                        "description": description
                    })
            if len(categories) >= 10:
                break
        return categories

def _generate_related_categories(category, general_persona_keywords=None, content_persona_keywords=None, network_persona_keywords=None):
    """
    Generate 3 unique, professional related categories for a given category using AI.
    """
    try:
        # Improved prompt for standard professional terminology
        prompt = f"""
        You are a LinkedIn content strategist.
        For the category: "{category}", generate 3 related PROFESSIONAL content categories.

        RULES:
        1. Use natural, standard industry terms (Native English).
        2. NO hype words (No "Secrets", "Hacks", "Explosion", "Ninja", "Rockstar").
        3. Format: Title Case, 2-3 words. No hyphens.
        4. Focus on professional expertise and industry trends.
        
        Input: "{category}"
        
        Bad Examples: "App Monetization Secrets", "Coding Beast", "Quality Crush", "Agile-insights"
        Good Examples: "Revenue Models", "Clean Architecture", "QA Automation", "Agile Methodologies"
        
        Persona context (for relevance):
        - General keywords: {', '.join(general_persona_keywords) if general_persona_keywords else 'None'}
        
        Return the response in this exact JSON format:
        {{
            "categories": [
                {{"name": "Category Name", "description": "Brief professional description"}},
                {{"name": "Category Name", "description": "Brief professional description"}},
                {{"name": "Category Name", "description": "Brief professional description"}}
            ]
        }}
        
        Return ONLY the JSON response, no additional text.
        """
        
        response = model.generate_content(prompt)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        
        # Parse the JSON response
        result = json.loads(response_text)
        if "categories" in result:
            return result["categories"]
        else:
            raise ValueError("No 'categories' key found in response")
            
    except Exception as e:
        print(f"Error generating related categories for '{category}': {e}")
        
        # Safe Fallback Logic
        clean_cat = category.strip().title()
        return [
            {
                "name": f"{clean_cat} Trends",
                "description": f"Latest developments and trends in {category.lower()}"
            },
            {
                "name": f"{clean_cat} Strategy",
                "description": f"Strategic approaches to {category.lower()}"
            },
            {
                "name": f"{clean_cat} Best Practices",
                "description": f"Industry standards and tips for {category.lower()}"
            }
        ]